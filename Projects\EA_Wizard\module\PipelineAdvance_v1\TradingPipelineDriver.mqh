#property strict

#include "TradingPipelineDriverBase.mqh"

//+------------------------------------------------------------------+
//| 常量定義                                                         |
//+------------------------------------------------------------------+
#define TRADING_PIPELINE_DRIVER_NAME "TradingPipelineDriver"
#define TRADING_PIPELINE_DRIVER_TYPE "PipelineDriver"
#define DEFAULT_MAX_CONTAINERS 20
#define DEFAULT_MAX_REGISTRATIONS 100

//+------------------------------------------------------------------+
//| 交易流水線驅動器 - 單例模式                                     |
//| 繼承自 TradingPipelineDriverBase 抽象基類                       |
//| 實現具體的初始化和配置邏輯                                       |
//+------------------------------------------------------------------+
class TradingPipelineDriver : public TradingPipelineDriverBase
{
private:
    static TradingPipelineDriver* s_instance;           // 單例實例

    // 私有構造函數（單例模式）
    TradingPipelineDriver(string name = TRADING_PIPELINE_DRIVER_NAME,
                         string type = TRADING_PIPELINE_DRIVER_TYPE)
        : TradingPipelineDriverBase(name, type)
    {
        // 自動初始化
        if(!Initialize())
        {
            SetResult(false, "驅動器初始化失敗", ERROR_LEVEL_ERROR);
        }
    }

public:
    // 獲取單例實例
    static TradingPipelineDriver* GetInstance()
    {
        if(s_instance == NULL)
        {
            s_instance = new TradingPipelineDriver();
        }
        return s_instance;
    }

    // 析構函數
    virtual ~TradingPipelineDriver() {}

    // 釋放單例實例
    static void ReleaseInstance()
    {
        if(s_instance != NULL)
        {
            delete s_instance;
            s_instance = NULL;
        }
    }

protected:
    // 實現抽象方法 - 初始化核心組件
    virtual bool InitializeComponents() override
    {
        // 1. 創建容器管理器
        if(!InitializeManager())
        {
            SetResult(false, "容器管理器初始化失敗", ERROR_LEVEL_ERROR);
            return false;
        }

        // 2. 創建註冊器（依賴管理器）
        if(!InitializeRegistry())
        {
            SetResult(false, "註冊器初始化失敗", ERROR_LEVEL_ERROR);
            return false;
        }

        // 3. 創建探索器（依賴註冊器）
        if(!InitializeExplorer())
        {
            SetResult(false, "探索器初始化失敗", ERROR_LEVEL_ERROR);
            return false;
        }

        SetResult(true, "所有組件初始化完成", ERROR_LEVEL_INFO);
        return true;
    }

    // 實現抽象方法 - 設置配置
    virtual bool SetupConfiguration() override
    {
        return SetupDefaultConfigurationInternal();
    }

    //+------------------------------------------------------------------+
    //| 設置默認配置 - 公共介面                                         |
    //+------------------------------------------------------------------+
    virtual bool SetupDefaultConfiguration() override
    {
        return SetupDefaultConfigurationInternal();
    }

private:
    //+------------------------------------------------------------------+
    //| 設置默認配置 - 內部實現                                         |
    //+------------------------------------------------------------------+
    bool SetupDefaultConfigurationInternal()
    {
        if(m_manager == NULL || m_registry == NULL || m_explorer == NULL)
        {
            SetResult(false, "核心組件未初始化，無法設置默認配置", ERROR_LEVEL_ERROR);
            return false;
        }

        // 創建默認的事件容器
        bool success = true;

        // 為每個事件創建容器
        success &= CreateDefaultStageContainer(TRADING_INIT, "初始化容器");
        success &= CreateDefaultStageContainer(TRADING_TICK, "Tick處理容器");
        success &= CreateDefaultStageContainer(TRADING_DEINIT, "清理容器");

        // 為每個階段創建對應的容器（如果需要的話）
        success &= CreateDefaultStageContainerForStage(INIT_START, "初始化開始容器");
        success &= CreateDefaultStageContainerForStage(INIT_COMPLETE, "初始化完成容器");
        success &= CreateDefaultStageContainerForStage(TICK_DATA_FEED, "數據饋送容器");

        if(success)
        {
            SetResult(true, "默認配置設置成功", ERROR_LEVEL_INFO);
        }
        else
        {
            SetResult(false, "默認配置設置部分失敗", ERROR_LEVEL_WARNING);
        }

        return success;
    }

private:


    //+------------------------------------------------------------------+
    //| 初始化容器管理器                                                 |
    //+------------------------------------------------------------------+
    bool InitializeManager()
    {
        m_manager = new TradingPipelineContainerManager(
            "MainContainerManager",
            "ContainerManager",
            true,  // owned
            DEFAULT_MAX_CONTAINERS
        );

        if(m_manager == NULL)
        {
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 初始化註冊器                                                     |
    //+------------------------------------------------------------------+
    bool InitializeRegistry()
    {
        if(m_manager == NULL)
        {
            return false;
        }

        m_registry = new TradingPipelineRegistry(
            m_manager,
            "MainRegistry",
            "PipelineRegistry",
            DEFAULT_MAX_REGISTRATIONS,
            true  // owned
        );

        if(m_registry == NULL)
        {
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 初始化探索器                                                     |
    //+------------------------------------------------------------------+
    bool InitializeExplorer()
    {
        if(m_registry == NULL)
        {
            return false;
        }

        m_explorer = new TradingPipelineExplorer(
            m_registry,
            "MainExplorer",
            "TradingPipelineExplorer",
            "主要交易流水線探索器"
        );

        if(m_explorer == NULL)
        {
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 創建默認階段容器                                                 |
    //+------------------------------------------------------------------+
    bool CreateDefaultStageContainer(ENUM_TRADING_EVENT event, string description)
    {
        if(m_registry == NULL)
        {
            return false;
        }

        // 創建容器名稱
        string containerName = "Container_" + TradingEventUtils::EventToString(event);

        // 創建容器
        TradingPipelineContainer* container = new TradingPipelineContainer(
            containerName,
            description,
            "TradingPipelineContainer",
            true,  // owned
            10     // maxPipelines
        );

        if(container == NULL)
        {
            return false;
        }

        // 註冊容器
        bool registered = m_registry.Register(event, container);
        if(!registered)
        {
            delete container;
            return false;
        }

        return true;
    }

    //+------------------------------------------------------------------+
    //| 為特定階段創建默認容器                                           |
    //+------------------------------------------------------------------+
    bool CreateDefaultStageContainerForStage(ENUM_TRADING_STAGE stage, string description)
    {
        if(m_registry == NULL)
        {
            return false;
        }

        // 創建容器名稱
        string containerName = "Container_" + TradingEventUtils::StageToString(stage);

        // 創建容器
        TradingPipelineContainer* container = new TradingPipelineContainer(
            containerName,
            description,
            "TradingPipelineContainer",
            true,  // owned
            10     // maxPipelines
        );

        if(container == NULL)
        {
            return false;
        }

        // 註冊容器到階段
        bool registered = m_registry.Register(stage, container);
        if(!registered)
        {
            delete container;
            return false;
        }

        return true;
    }
};

//+------------------------------------------------------------------+
//| 靜態成員初始化                                                   |
//+------------------------------------------------------------------+
TradingPipelineDriver* TradingPipelineDriver::s_instance = NULL;
